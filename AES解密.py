import base64
import json
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad


encrypted_data_b64 = "eqNTwiXs+DyHu3Riq6drycCRYv5PkGcZfAY6T4p6jE/YWouR0IKQ9CcQ/9ct3WQ53Uc/bWnl761MQdrKIQikPBhuzUxjhjRpsvfQbBDsdQfknCzhxhUvuaKNgvtigoQc7Eoawsbr+wgIpOCkzi0uAw=="
key_str = "AaBchAcOmFAr50HY"
iv_str = "obupp7W95LHe3ewL"
# ------------------------------------

try:
    # 1. 将 Key 和 IV 转换为 bytes (UTF-8 编码)
    key = key_str.encode('utf-8')
    iv = iv_str.encode('utf-8')

    # 2. 将 Base64 编码的加密数据解码为 bytes
    encrypted_data = base64.b64decode(encrypted_data_b64)

    # 3. 创建 AES 解密器，使用 CBC 模式
    cipher = AES.new(key, AES.MODE_CBC, iv)

    # 4. 解密数据，并使用 PKCS7 方式去除填充
    decrypted_padded_data = cipher.decrypt(encrypted_data)
    decrypted_data = unpad(decrypted_padded_data, AES.block_size, style='pkcs7')

    # 5. 将解密后的 bytes 转换为 UTF-8 字符串
    decrypted_text = decrypted_data.decode('utf-8')

    # 6. (可选) 如果解密结果是JSON字符串，可以将其解析为Python对象
    final_object = json.loads(decrypted_text)

    print("✅ 解密成功!")
    print("--- 解密后的JSON对象 ---")
    print(json.dumps(final_object, indent=4, ensure_ascii=False))

except Exception as e:
    print(f"❌ 解密失败: {e}")
    print("--- 请检查 ---")
    print("1. 密钥、IV 和加密数据是否正确无误？")
    print("2. 加密数据是否是有效的Base64字符串？")
    print("3. (常见错误) 如果提示'Padding is incorrect'，通常意味着密钥或IV错误。")